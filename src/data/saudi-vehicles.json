{"vehicles": [{"id": "VEH_001", "name": "شاحنة نقل البضائع - الرياض إلى جدة", "nameEn": "Cargo Truck - Riyadh to Jeddah", "coordinates": [24.2, 45.5], "type": "cargo", "status": "moving", "speed": 85, "heading": 270, "destination": "ميناء جدة الإسلامي", "destinationEn": "Jeddah Islamic Port", "origin": "مطار الملك خالد الدولي", "originEn": "King Khalid International Airport", "plateNumber": "أ ب ج 1234", "driver": "<PERSON><PERSON><PERSON><PERSON> محمد", "driverEn": "<PERSON>", "cargo": "مواد غذائية", "cargoEn": "Food Products"}, {"id": "VEH_002", "name": "شاحنة صهريج - الد<PERSON>ام إلى الرياض", "nameEn": "Tanker Truck - Dammam to Riyadh", "coordinates": [26.2, 49.5], "type": "tanker", "status": "moving", "speed": 90, "heading": 225, "destination": "مطار الملك خالد الدولي", "destinationEn": "King Khalid International Airport", "origin": "ميناء الملك عبدالعزيز", "originEn": "King Abdulaziz Port", "plateNumber": "د هـ و 5678", "driver": "سالم علي", "driverEn": "Salem Ali", "cargo": "وقود", "cargoEn": "Fuel"}, {"id": "VEH_003", "name": "شاحنة حاويات - جدة إلى الرياض", "nameEn": "Container Truck - Jeddah to Riyadh", "coordinates": [22.5, 41.0], "type": "container", "status": "moving", "speed": 75, "heading": 90, "destination": "مطار الملك خالد الدولي", "destinationEn": "King Khalid International Airport", "origin": "ميناء جدة الإسلامي", "originEn": "Jeddah Islamic Port", "plateNumber": "ز ح ط 9012", "driver": "م<PERSON>م<PERSON> عبد<PERSON><PERSON><PERSON>ه", "driverEn": "<PERSON>", "cargo": "إلكترونيات", "cargoEn": "Electronics"}, {"id": "VEH_004", "name": "سيارة نقل خفيف - الرياض إلى الدمام", "nameEn": "Light Delivery Vehicle - Riyadh to Dammam", "coordinates": [25.0, 47.5], "type": "delivery", "status": "moving", "speed": 60, "heading": 90, "destination": "مطار الملك فهد الدولي", "destinationEn": "King Fahd International Airport", "origin": "مطار الملك خالد الدولي", "originEn": "King Khalid International Airport", "plateNumber": "ي ك ل 3456", "driver": "عبدا<PERSON>رحمن سعد", "driverEn": "<PERSON><PERSON><PERSON>"}, {"id": "VEH_005", "name": "حافلة نقل الركاب - جدة إلى المدينة", "nameEn": "Passenger Bus - Jeddah to Medina", "coordinates": [23.5, 39.5], "type": "bus", "status": "moving", "speed": 90, "heading": 45, "destination": "مطار الأمير محمد بن عبدالعزيز", "destinationEn": "Prince <PERSON> Airport", "origin": "مطار الملك عبدالعزيز الدولي", "originEn": "King Abdulaziz International Airport", "plateNumber": "م ن س 7890", "driver": "<PERSON><PERSON><PERSON><PERSON> أحمد", "driverEn": "<PERSON>", "passengers": 45}, {"id": "VEH_006", "name": "سيارة إسعاف - الدمام", "nameEn": "Ambulance - <PERSON><PERSON><PERSON>", "coordinates": [26.3927, 50.1059], "type": "emergency", "status": "emergency", "speed": 120, "heading": 225, "destination": "مركز شرطة الدمام", "destinationEn": "Dammam Police Station", "origin": "ميناء الملك عبدالعزيز", "originEn": "King Abdulaziz Port", "plateNumber": "ع ف ص 1122", "driver": "طا<PERSON><PERSON> محمد", "driverEn": "<PERSON><PERSON><PERSON>", "priority": "high"}, {"id": "VEH_007", "name": "سيارة شرطة - الرياض", "nameEn": "Police Car - Riyadh", "coordinates": [24.7136, 46.6753], "type": "police", "status": "patrol", "speed": 50, "heading": 135, "destination": "نقطة تفتيش طريق الرياض - جدة", "destinationEn": "Riyadh-Jeddah Highway Checkpoint", "origin": "مركز شرطة الملز", "originEn": "Al-Malaz Police Station", "plateNumber": "ق ر ش 3344", "officer": "الن<PERSON><PERSON><PERSON> أحمد", "officerEn": "Captain <PERSON>"}, {"id": "VEH_008", "name": "شاحنة نقل - أبها إلى جازان", "nameEn": "Cargo Truck - Abha to Jazan", "coordinates": [17.5, 42.6], "type": "cargo", "status": "moving", "speed": 70, "heading": 180, "destination": "ميناء جازان", "destinationEn": "Jazan Port", "origin": "مطار الأمير عبدالمحسن بن عبدالعزيز", "originEn": "Prince <PERSON>z Airport", "plateNumber": "أ هـ ب 4455", "driver": "عبدالله سعيد", "driverEn": "<PERSON>", "cargo": "منتجات زراعية", "cargoEn": "Agricultural Products"}, {"id": "VEH_009", "name": "شاحنة صهريج - ينبع إلى الرياض", "nameEn": "Tanker Truck - Yanbu to Riyadh", "coordinates": [23.5, 40.5], "type": "tanker", "status": "moving", "speed": 85, "heading": 90, "destination": "مطار الملك خالد الدولي", "destinationEn": "King Khalid International Airport", "origin": "ميناء ينبع التجاري", "originEn": "Yanbu Commercial Port", "plateNumber": "ي ن ب 6677", "driver": "<PERSON><PERSON><PERSON> الحربي", "driverEn": "Fahd Al-Harbi", "cargo": "مواد كيميائية", "cargoEn": "Chemical Materials"}, {"id": "VEH_010", "name": "حافلة سياحية - تبوك إلى العلا", "nameEn": "Tourist Bus - Tabuk to AlUla", "coordinates": [26.0, 37.0], "type": "bus", "status": "moving", "speed": 80, "heading": 180, "destination": "مطار الأمير عبدالمجيد بن عبدالعزيز", "destinationEn": "Prince <PERSON> bin <PERSON> Airport", "origin": "مطار تبوك الإقليمي", "originEn": "Tabuk Regional Airport", "plateNumber": "ت ب ك 8899", "driver": "ما<PERSON><PERSON> القحطاني", "driverEn": "<PERSON><PERSON>", "passengers": 35}, {"id": "VEH_011", "name": "شاحنة نقل - الجبيل إلى الدمام", "nameEn": "Cargo Truck - Jubail to Dammam", "coordinates": [26.8, 49.8], "type": "cargo", "status": "moving", "speed": 65, "heading": 180, "destination": "ميناء الملك عبدالعزيز", "destinationEn": "King Abdulaziz Port", "origin": "ميناء الجبيل التجاري", "originEn": "Jubail Commercial Port", "plateNumber": "ج <PERSON> 1111", "driver": "عبدا<PERSON><PERSON><PERSON>يز الشمري", "driverEn": "Abdulaziz <PERSON>", "cargo": "مواد بتروكيميائية", "cargoEn": "Petrochemical Materials"}, {"id": "VEH_012", "name": "سيارة إسعاف - جدة", "nameEn": "Ambulance - Jeddah", "coordinates": [21.5, 39.2], "type": "emergency", "status": "emergency", "speed": 110, "heading": 45, "destination": "مركز شرطة البلد", "destinationEn": "Al-Balad Police Station", "origin": "مطار الملك عبدالعزيز الدولي", "originEn": "King Abdulaziz International Airport", "plateNumber": "ج د ة 2222", "driver": "محم<PERSON> الغا<PERSON><PERSON>ي", "driverEn": "<PERSON>", "priority": "high"}, {"id": "VEH_013", "name": "شاحنة حاويات - ض<PERSON>اء إلى تبوك", "nameEn": "Container Truck - Dhiba to Tabuk", "coordinates": [27.8, 36.2], "type": "container", "status": "moving", "speed": 80, "heading": 135, "destination": "مطار تبوك الإقليمي", "destinationEn": "Tabuk Regional Airport", "origin": "ميناء ضباء", "originEn": "Dhiba Port", "plateNumber": "ض ب أ 3333", "driver": "فيصل العنزي", "driverEn": "Faisal Al-Anzi", "cargo": "بضائع مستوردة", "cargoEn": "Imported Goods"}, {"id": "VEH_014", "name": "سيارة شرطة - أبها", "nameEn": "Police Car - Abha", "coordinates": [18.2, 42.5], "type": "police", "status": "patrol", "speed": 40, "heading": 270, "destination": "نقطة تفتيش طريق جازان - أبها", "destinationEn": "Jazan-Abha Highway Checkpoint", "origin": "مركز شرطة أبها", "originEn": "Abha Police Station", "plateNumber": "أ ب ه 4444", "officer": "الرائد سعد", "officerEn": "Major <PERSON><PERSON>"}, {"id": "VEH_015", "name": "حافلة مدرسية - الحائل", "nameEn": "School Bus - Hail", "coordinates": [27.5, 41.7], "type": "bus", "status": "moving", "speed": 50, "heading": 90, "destination": "مطار الأمير عبدالمجيد بن عبدالعزيز", "destinationEn": "Prince <PERSON> bin <PERSON> Airport", "origin": "مركز شرطة الحائل", "originEn": "Hail Police Station", "plateNumber": "ح ا ئ 5555", "driver": "يوس<PERSON> المطيري", "driverEn": "<PERSON><PERSON><PERSON>", "passengers": 25}, {"id": "VEH_016", "name": "شاحنة نقل - القصيم إلى الرياض", "nameEn": "Cargo Truck - <PERSON><PERSON>im to Riyadh", "coordinates": [25.5, 44.5], "type": "cargo", "status": "moving", "speed": 95, "heading": 135, "destination": "مطار الملك خالد الدولي", "destinationEn": "King Khalid International Airport", "origin": "مطار الأمير نايف بن عبدالعزيز الإقليمي", "originEn": "Prince <PERSON><PERSON> Abdulaziz Regional Airport", "plateNumber": "ق ص م 6666", "driver": "عبدالرحمن التميمي", "driverEn": "<PERSON><PERSON><PERSON>", "cargo": "منتجات زراعية", "cargoEn": "Agricultural Products"}, {"id": "VEH_017", "name": "سيارة إطفاء - الرياض", "nameEn": "Fire Truck - Riyadh", "coordinates": [24.6, 46.7], "type": "emergency", "status": "emergency", "speed": 100, "heading": 270, "destination": "مطار الملك خالد الدولي", "destinationEn": "King Khalid International Airport", "origin": "مركز شرطة الملز", "originEn": "Al-Malaz Police Station", "plateNumber": "ر ي ض 7777", "driver": "سلطان الدوسري", "driverEn": "<PERSON>", "priority": "high"}, {"id": "VEH_018", "name": "شاحنة صهريج - رابغ إلى جدة", "nameEn": "Tanker Truck - Rabigh to Jeddah", "coordinates": [22.2, 39.0], "type": "tanker", "status": "moving", "speed": 75, "heading": 315, "destination": "ميناء جدة الإسلامي", "destinationEn": "Jeddah Islamic Port", "origin": "ميناء رابغ", "originEn": "Rabigh Port", "plateNumber": "ر ا ب 8888", "driver": "ناصر الحارثي", "driverEn": "<PERSON><PERSON>", "cargo": "مشتقات نفطية", "cargoEn": "Petroleum Products"}, {"id": "VEH_019", "name": "حافلة سياحية - العلا إلى المدينة", "nameEn": "Tourist Bus - Al<PERSON>la to Medina", "coordinates": [22.0, 40.5], "type": "bus", "status": "moving", "speed": 85, "heading": 90, "destination": "مطار الأمير محمد بن عبدالعزيز", "destinationEn": "Prince <PERSON> Airport", "origin": "مطار الأمير عبدالمجيد بن عبدالعزيز", "originEn": "Prince <PERSON> bin <PERSON> Airport", "plateNumber": "ع ل ا 9999", "driver": "حسام الجهني", "driverEn": "<PERSON><PERSON><PERSON>", "passengers": 40}, {"id": "VEH_020", "name": "سيارة شرطة - جازان", "nameEn": "Police Car - Jazan", "coordinates": [16.9, 42.6], "type": "police", "status": "patrol", "speed": 60, "heading": 45, "destination": "مطار الأمير عبدالله بن عبدالعزيز الإقليمي", "destinationEn": "Prince <PERSON> bin <PERSON>aziz Regional Airport", "origin": "مركز شرطة جازان", "originEn": "Jazan Police Station", "plateNumber": "ج ز ن 1010", "officer": "الملازم خالد", "officerEn": "Lieutenant <PERSON>"}, {"id": "VEH_021", "name": "شاحنة نقل - المدينة إلى ينبع", "nameEn": "Cargo Truck - Medina to Yanbu", "coordinates": [24.0, 39.0], "type": "cargo", "status": "moving", "speed": 70, "heading": 270, "destination": "ميناء ينبع التجاري", "destinationEn": "Yanbu Commercial Port", "origin": "مطار الأمير محمد بن عبدالعزيز", "originEn": "Prince <PERSON> Airport", "plateNumber": "م د ن 1111", "driver": "ع<PERSON><PERSON> الأحمدي", "driverEn": "<PERSON><PERSON>", "cargo": "مواد غذائية", "cargoEn": "Food Products"}, {"id": "VEH_022", "name": "سيارة إسعاف - تبوك", "nameEn": "Ambulance - Tabuk", "coordinates": [28.4, 36.6], "type": "emergency", "status": "emergency", "speed": 115, "heading": 180, "destination": "مركز شرطة تبوك", "destinationEn": "Tabuk Police Station", "origin": "مطار تبوك الإقليمي", "originEn": "Tabuk Regional Airport", "plateNumber": "ت ب و 1212", "driver": "وليد الرويلي", "driverEn": "Waleed Al-Ruwaili", "priority": "high"}, {"id": "VEH_023", "name": "شاحنة حاويات - الد<PERSON>ام إلى الجبيل", "nameEn": "Container Truck - Dammam to Jubail", "coordinates": [26.5, 49.7], "type": "container", "status": "moving", "speed": 85, "heading": 45, "destination": "ميناء الجبيل التجاري", "destinationEn": "Jubail Commercial Port", "origin": "مطار الملك فهد الدولي", "originEn": "King Fahd International Airport", "plateNumber": "د م م 1313", "driver": "<PERSON><PERSON><PERSON><PERSON> العجمي", "driverEn": "<PERSON><PERSON>", "cargo": "معدات صناعية", "cargoEn": "Industrial Equipment"}, {"id": "VEH_024", "name": "حافلة نقل - الرياض إلى القصيم", "nameEn": "Passenger Bus - Riyadh to Qassim", "coordinates": [25.0, 45.0], "type": "bus", "status": "moving", "speed": 90, "heading": 315, "destination": "مطار الأمير نايف بن عبدالعزيز الإقليمي", "destinationEn": "Prince <PERSON><PERSON> Abdulaziz Regional Airport", "origin": "مطار الملك خالد الدولي", "originEn": "King Khalid International Airport", "plateNumber": "ر ق ص 1414", "driver": "بندر السبيعي", "driverEn": "Bandar Al-Subai'i", "passengers": 50}, {"id": "VEH_025", "name": "سيارة شرطة - المدينة المنورة", "nameEn": "Police Car - Medina", "coordinates": [24.5, 39.6], "type": "police", "status": "patrol", "speed": 45, "heading": 225, "destination": "نقطة تفتيش طريق جدة - المدينة", "destinationEn": "Jeddah-Medina Highway Checkpoint", "origin": "مركز شرطة المدينة المنورة", "originEn": "Medina Police Station", "plateNumber": "م د ي 1515", "officer": "العقي<PERSON> محمد", "officerEn": "Colonel <PERSON>"}]}